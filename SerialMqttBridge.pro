QT = core

CONFIG += c++17 cmdline

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = SerialMqttBridge

# 包含模块
include(src/config/config.pri)
include(src/protocol/protocol.pri)
#include(src/common/common.pri)
include(src/serial/serial.pri)
include(src/mqtt/mqtt.pri)
include(src/bridge/bridge.pri)

SOURCES += \
        src/main.cpp

# QMQTT库配置
INCLUDEPATH += $$PWD/src/qmqtt
LIBS += -L$$PWD/lib -lQt5Qmqtt

# 日志库配置
INCLUDEPATH += $$PWD/include/log
LIBS += -L$$PWD/lib -llog

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
